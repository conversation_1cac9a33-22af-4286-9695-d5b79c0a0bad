using Microsoft.Extensions.Options;
using MongoDB.Driver;
using Polly;
using Polly.Retry;
using Zin.Logging.Domain.Entities;
using Zin.Logging.Domain.Interfaces;
using Zin.Logging.Infrastructure.Config;

namespace Zin.Logging.Infrastructure.Mongo;

public class LogRepository : ILogRepository
{
    private readonly IMongoCollection<LogEvent> _logsCollection;
    private readonly AsyncRetryPolicy _retryPolicy;

    public LogRepository(IMongoClient client, IOptions<MongoDbSettings> settings)
    {
        var database = client.GetDatabase(settings.Value.DatabaseName);
        _logsCollection = database.GetCollection<LogEvent>(settings.Value.CollectionName);

        _retryPolicy = Policy
            .Handle<MongoException>()
            .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                (exception, timeSpan, retryCount, context) =>
                {
                    Console.WriteLine($"Retry {retryCount} due to {exception.Message}. Waiting {timeSpan.TotalSeconds}s before next retry.");
                });
    }

    public async Task AddLogAsync(LogEvent logEvent)
    {
        await _retryPolicy.ExecuteAsync(async () =>
        {
            await _logsCollection.InsertOneAsync(logEvent);
        });
    }

    public async Task AddLogsAsync(IEnumerable<LogEvent> logEvents)
    {
        await _retryPolicy.ExecuteAsync(async () =>
        {
            await _logsCollection.InsertManyAsync(logEvents, new InsertManyOptions { IsOrdered = false });
        });
    }

    public async Task<IEnumerable<LogEvent>> GetLogsAsync(int page, int pageSize)
    {
        return await _retryPolicy.ExecuteAsync(async () =>
        {
            return await _logsCollection.Find(_ => true)
                .SortByDescending(l => l.Ts)
                .Skip((page - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();
        });
    }
}
