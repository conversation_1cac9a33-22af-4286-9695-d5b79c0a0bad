using FluentValidation;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.Application.Validators;

public class LogInputDtoValidator : AbstractValidator<LogInputDto>
{
    public LogInputDtoValidator()
    {
        // Validações básicas obrigatórias
        RuleFor(x => x.Ts)
            .NotEmpty()
            .WithMessage("Timestamp é obrigatório");

        RuleFor(x => x.Level)
            .NotEmpty()
            .WithMessage("Level é obrigatório")
            .Must(BeValidLogLevel)
            .WithMessage("Level inválido. Valores válidos: Trace, Debug, Information, Warning, Error, Critical");

        RuleFor(x => x.Message)
            .NotEmpty()
            .WithMessage("Message é obrigatória")
            .MaximumLength(5000)
            .WithMessage("Message muito longa (máximo 5000 caracteres)");

        RuleFor(x => x.Tenant)
            .NotEmpty()
            .WithMessage("Tenant é obrigatório")
            .MaximumLength(50)
            .WithMessage("Tenant muito longo (máximo 50 caracteres)");

        // Validações opcionais simples
        RuleFor(x => x.Host)
            .MaximumLength(100)
            .WithMessage("Host muito longo (máximo 100 caracteres)")
            .When(x => !string.IsNullOrWhiteSpace(x.Host));

        // Validação simples de Exception (se presente)
        RuleFor(x => x.Exception!.Type)
            .MaximumLength(500)
            .WithMessage("Exception Type muito longo (máximo 500 caracteres)")
            .When(x => x.Exception != null && !string.IsNullOrWhiteSpace(x.Exception.Type));

        RuleFor(x => x.Exception!.Message)
            .MaximumLength(2000)
            .WithMessage("Exception Message muito longa (máximo 2000 caracteres)")
            .When(x => x.Exception != null && !string.IsNullOrWhiteSpace(x.Exception.Message));

        // Validação simples de Trace (se presente)
        RuleFor(x => x.Trace!.CorrelationId)
            .Must(BeValidGuid)
            .WithMessage("CorrelationId deve ser um GUID válido")
            .When(x => x.Trace != null && !string.IsNullOrWhiteSpace(x.Trace.CorrelationId));
    }

    private static bool BeValidLogLevel(string? level)
    {
        if (string.IsNullOrWhiteSpace(level)) return false;

        var validLevels = new[] { "Trace", "Debug", "Information", "Warning", "Error", "Critical" };
        return validLevels.Contains(level, StringComparer.OrdinalIgnoreCase);
    }

    private static bool BeValidGuid(string? correlationId)
    {
        return string.IsNullOrWhiteSpace(correlationId) || Guid.TryParse(correlationId, out _);
    }
}
