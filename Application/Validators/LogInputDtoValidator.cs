using FluentValidation;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.Application.Validators;

public class LogInputDtoValidator : AbstractValidator<LogInputDto>
{
    public LogInputDtoValidator()
    {
        RuleFor(x => x.Ts)
            .NotEmpty()
            .WithMessage("Timestamp é obrigatório");

        RuleFor(x => x.Level)
            .NotEmpty()
            .WithMessage("Level é obrigatório");

        RuleFor(x => x.Message)
            .NotEmpty()
            .WithMessage("Message é obrigatória")
            .MaximumLength(5000)
            .WithMessage("Message muito longa (máximo 5000 caracteres)");

        RuleFor(x => x.Tenant)
            .NotEmpty()
            .WithMessage("Tenant é obrigatório")
            .MaximumLength(50)
            .WithMessage("Tenant muito longo (máximo 50 caracteres)");

        RuleFor(x => x.Host)
            .MaximumLength(100)
            .WithMessage("Host muito longo (máximo 100 caracteres)")
            .When(x => !string.IsNullOrWhiteSpace(x.Host));

        RuleFor(x => x.Trace)
            .SetValidator(new TraceInfoDtoValidator()!)
            .When(x => x.Trace != null);
    }
}
