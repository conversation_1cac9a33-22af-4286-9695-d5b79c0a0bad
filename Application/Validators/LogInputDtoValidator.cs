using FluentValidation;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.Application.Validators;

public class LogInputDtoValidator : AbstractValidator<LogInputDto>
{
    public LogInputDtoValidator()
    {
        RuleFor(x => x.Ts)
            .NotEmpty()
            .WithMessage("Timestamp é obrigatório")
            .Must(BeValidTimestamp)
            .WithMessage("Timestamp não pode ser futuro");

        RuleFor(x => x.Level)
            .NotEmpty()
            .WithMessage("Level é obrigatório")
            .Must(BeValidLogLevel)
            .WithMessage("Level inválido. Valores válidos: Trace, Debug, Information, Warning, Error, Critical");

        RuleFor(x => x.Message)
            .NotEmpty()
            .WithMessage("Message é obrigatória")
            .MaximumLength(5000)
            .WithMessage("Message muito longa (máximo 5000 caracteres)");

        RuleFor(x => x.Tenant)
            .NotEmpty()
            .WithMessage("Tenant é obrigatório")
            .MaximumLength(50)
            .WithMessage("Tenant muito longo (máximo 50 caracteres)");

        RuleFor(x => x.Host)
            .MaximumLength(100)
            .WithMessage("Host muito longo (máximo 100 caracteres)")
            .When(x => !string.IsNullOrWhiteSpace(x.Host));

        RuleFor(x => x.Props)
            .Must(HaveValidPropsCount)
            .WithMessage("Máximo 100 propriedades por log")
            .When(x => x.Props != null);

        RuleFor(x => x.Exception)
            .SetValidator(new ExceptionInfoDtoValidator()!)
            .When(x => x.Exception != null);

        RuleFor(x => x.Trace)
            .SetValidator(new TraceInfoDtoValidator()!)
            .When(x => x.Trace != null);
    }

    private static bool BeValidTimestamp(DateTime timestamp)
    {
        return timestamp != default && timestamp <= DateTime.UtcNow.AddMinutes(5);
    }

    private static bool BeValidLogLevel(string? level)
    {
        if (string.IsNullOrWhiteSpace(level)) return false;
        
        var validLevels = new[] { "Trace", "Debug", "Information", "Warning", "Error", "Critical" };
        return validLevels.Contains(level, StringComparer.OrdinalIgnoreCase);
    }

    private static bool HaveValidPropsCount(Dictionary<string, object>? props)
    {
        return props == null || props.Count <= 100;
    }
}
