using FluentValidation;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.Application.Validators;

public class ExceptionInfoDtoValidator : AbstractValidator<ExceptionInfoDto>
{
    public ExceptionInfoDtoValidator()
    {
        RuleFor(x => x.Type)
            .MaximumLength(500)
            .WithMessage("Exception Type muito longo (máximo 500 caracteres)")
            .When(x => !string.IsNullOrWhiteSpace(x.Type));

        RuleFor(x => x.Message)
            .MaximumLength(2000)
            .WithMessage("Exception Message muito longa (máximo 2000 caracteres)")
            .When(x => !string.IsNullOrWhiteSpace(x.Message));

        RuleFor(x => x.Stack)
            .MaximumLength(50000)
            .WithMessage("Stack trace muito longo (máximo 50000 caracteres)")
            .When(x => !string.IsNullOrWhiteSpace(x.Stack));
    }
}
