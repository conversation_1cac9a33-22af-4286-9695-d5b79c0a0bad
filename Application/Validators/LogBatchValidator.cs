using FluentValidation;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.Application.Validators;

public class LogBatchValidator : AbstractValidator<IEnumerable<LogInputDto>>
{
    public LogBatchValidator()
    {
        RuleFor(x => x)
            .NotNull()
            .WithMessage("Lista de logs não pode ser nula");

        RuleFor(x => x)
            .Must(HaveAtLeastOneLog)
            .WithMessage("Nenhum log fornecido")
            .When(x => x != null);

        RuleFor(x => x)
            .Must(NotExceedMaxBatchSize)
            .WithMessage("Máximo 1000 logs por lote")
            .When(x => x != null);

        RuleForEach(x => x)
            .SetValidator(new LogInputDtoValidator())
            .When(x => x != null);
    }

    private static bool HaveAtLeastOneLog(IEnumerable<LogInputDto> logs)
    {
        return logs.Any();
    }

    private static bool NotExceedMaxBatchSize(IEnumerable<LogInputDto> logs)
    {
        return logs.Count() <= 1000;
    }
}
