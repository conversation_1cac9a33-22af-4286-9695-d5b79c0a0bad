using FluentValidation;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.Application.Validators;

public class TraceInfoDtoValidator : AbstractValidator<TraceInfoDto>
{
    public TraceInfoDtoValidator()
    {
        RuleFor(x => x.CorrelationId)
            .Must(BeValidGuid)
            .WithMessage("CorrelationId deve ser um GUID válido")
            .When(x => !string.IsNullOrWhiteSpace(x.CorrelationId));

        RuleFor(x => x.TraceId)
            .Must(BeValidTraceId)
            .WithMessage("TraceId deve ter formato hexadecimal de 32 caracteres")
            .When(x => !string.IsNullOrWhiteSpace(x.TraceId));

        RuleFor(x => x.SpanId)
            .Must(BeValidSpanId)
            .WithMessage("SpanId deve ter formato hexadecimal de 16 caracteres")
            .When(x => !string.IsNullOrWhiteSpace(x.SpanId));
    }

    private static bool BeValidGuid(string? correlationId)
    {
        return string.IsNullOrWhiteSpace(correlationId) || Guid.TryParse(correlationId, out _);
    }

    private static bool BeValidTraceId(string? traceId)
    {
        if (string.IsNullOrWhiteSpace(traceId)) return true;
        
        return traceId.Length == 32 &&
               traceId.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }

    private static bool BeValidSpanId(string? spanId)
    {
        if (string.IsNullOrWhiteSpace(spanId)) return true;
        
        return spanId.Length == 16 &&
               spanId.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }
}
