using System.Security.Cryptography;
using System.Text;
using FluentValidation;
using Zin.Logging.Application.DTOs;
using Zin.Logging.Application.Validators;
using Zin.Logging.Shared.DTOs;
using Zin.Logging.Domain.Entities;
using Zin.Logging.Domain.Interfaces;
using Zin.Logging.Shared.Utils;

namespace Zin.Logging.Application.Services;

public class LogService
{
    private readonly ILogRepository _logRepository;
    private readonly PiiMasker _piiMasker;
    private readonly IValidator<IEnumerable<LogInputDto>> _batchValidator;

    public LogService(ILogRepository logRepository, PiiMasker piiMasker, IValidator<IEnumerable<LogInputDto>> batchValidator)
    {
        _logRepository = logRepository;
        _piiMasker = piiMasker;
        _batchValidator = batchValidator;
    }

    public async Task SaveLogs(IEnumerable<LogInputDto> logInputs)
    {
        await ValidateAsync(logInputs);

        var logInputsList = logInputs.ToList();
        var logEvents = ProcessLogs(logInputsList);
        await _logRepository.AddLogsAsync(logEvents);
    }

    public async Task<IEnumerable<LogOutputDto>> GetLogsAsync(int page, int pageSize)
    {
        var logEvents = await _logRepository.GetLogsAsync(page, pageSize);
        return logEvents.Select(logEvent => new LogOutputDto
        {
            EventId = logEvent.EventId,
            Ts = logEvent.Ts,
            Level = logEvent.Level,
            Message = logEvent.Message,
            Props = logEvent.Props,
            Tenant = logEvent.Tenant,
            Host = logEvent.Host
        });
    }

    private async Task ValidateAsync(IEnumerable<LogInputDto> logInputs)
    {
        var validationResult = await _batchValidator.ValidateAsync(logInputs);

        if (!validationResult.IsValid)
        {
            var errors = string.Join("; ", validationResult.Errors.Select(e => e.ErrorMessage));
            throw new ValidationException($"Erro de validação: {errors}");
        }
    }

    private IEnumerable<LogEvent> ProcessLogs(List<LogInputDto> logInputsList)
    {
        return logInputsList.Select(logInput =>
        {
            _piiMasker.Mask(logInput);
            return new LogEvent
            {
                EventId = Guid.NewGuid(),
                Ts = logInput.Ts,
                Level = logInput.Level,
                Message = logInput.Message,
                Props = logInput.Props?.ToDictionary(p => p.Key, p => BsonConverter.ConvertJsonElement(p.Value)),
                Exception = CreateExceptionInfo(logInput.Exception),
                Trace = CreateTraceInfo(logInput.Trace),
                Tenant = logInput.Tenant,
                Host = logInput.Host,
                SchemaVersion = 1
            };
        });
    }

    private static Domain.Entities.ExceptionInfo? CreateExceptionInfo(ExceptionInfoDto? exceptionDto)
    {
        if (exceptionDto == null) return null;

        return new Domain.Entities.ExceptionInfo
        {
            Type = exceptionDto.Type,
            Message = exceptionDto.Message,
            Stack = exceptionDto.Stack,
            StackHash = CalculateStackHash(exceptionDto.Stack)
        };
    }

    private static Domain.Entities.TraceInfo? CreateTraceInfo(TraceInfoDto? traceDto)
    {
        if (traceDto == null) return null;

        return new Domain.Entities.TraceInfo
        {
            CorrelationId = traceDto.CorrelationId,
            TraceId = traceDto.TraceId,
            SpanId = traceDto.SpanId
        };
    }

    #region Utility Methods

    private static string? CalculateStackHash(string? stackTrace)
    {
        if (string.IsNullOrEmpty(stackTrace)) return null;

        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(stackTrace));
        return Convert.ToHexString(hashBytes)[..8];
    }

    #endregion


}
