using System.Security.Cryptography;
using System.Text;
using Zin.Logging.Application.DTOs;
using Zin.Logging.Shared.DTOs;
using Zin.Logging.Domain.Entities;
using Zin.Logging.Domain.Interfaces;
using Zin.Logging.Shared.Utils;

namespace Zin.Logging.Application.Services;

public class LogService
{
    private readonly ILogRepository _logRepository;
    private readonly PiiMasker _piiMasker;

    public LogService(ILogRepository logRepository, PiiMasker piiMasker)
    {
        _logRepository = logRepository;
        _piiMasker = piiMasker;
    }

    public async Task SaveLogs(IEnumerable<LogInputDto> logInputs)
    {
        // Validação do lote
        ValidateBatch(logInputs);

        // Validação individual de cada log
        var logInputsList = logInputs.ToList();
        for (int i = 0; i < logInputsList.Count; i++)
        {
            try
            {
                ValidateLogInput(logInputsList[i]);
            }
            catch (ArgumentException ex)
            {
                throw new ArgumentException($"Erro no log[{i}]: {ex.Message}", ex);
            }
        }

        var logEvents = logInputsList.Select(logInput =>
        {
            _piiMasker.Mask(logInput);
            return new LogEvent
            {
                EventId = Guid.NewGuid(),
                Ts = logInput.Ts,
                Level = logInput.Level,
                Message = logInput.Message,
                Props = logInput.Props?.ToDictionary(p => p.Key, p => BsonConverter.ConvertJsonElement(p.Value)),
                Exception = logInput.Exception != null ? new Domain.Entities.ExceptionInfo
                {
                    Type = logInput.Exception.Type,
                    Message = logInput.Exception.Message,
                    Stack = logInput.Exception.Stack,
                    StackHash = CalculateStackHash(logInput.Exception.Stack)
                } : null,
                Trace = logInput.Trace != null ? new Domain.Entities.TraceInfo
                {
                    CorrelationId = logInput.Trace.CorrelationId,
                    TraceId = logInput.Trace.TraceId,
                    SpanId = logInput.Trace.SpanId
                } : null,
                Tenant = logInput.Tenant,
                Host = logInput.Host,
                SchemaVersion = 1
            };
        });

        await _logRepository.AddLogsAsync(logEvents);
    }

    public async Task<IEnumerable<LogOutputDto>> GetLogsAsync(int page, int pageSize)
    {
        var logEvents = await _logRepository.GetLogsAsync(page, pageSize);
        return logEvents.Select(logEvent => new LogOutputDto
        {
            EventId = logEvent.EventId,
            Ts = logEvent.Ts,
            Level = logEvent.Level,
            Message = logEvent.Message,
            Props = logEvent.Props,
            Tenant = logEvent.Tenant,
            Host = logEvent.Host
        });
    }

    private static string? CalculateStackHash(string? stackTrace)
    {
        if (string.IsNullOrEmpty(stackTrace)) return null;

        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(stackTrace));
        return Convert.ToHexString(hashBytes)[..8];
    }

    private static void ValidateBatch(IEnumerable<LogInputDto> logInputs)
    {
        if (logInputs == null)
            throw new ArgumentNullException(nameof(logInputs), "Lista de logs não pode ser nula");

        var logsList = logInputs.ToList();

        if (!logsList.Any())
            throw new ArgumentException("Nenhum log fornecido");

        if (logsList.Count > 1000)
            throw new ArgumentException("Máximo 1000 logs por lote");
    }

    private static void ValidateLogInput(LogInputDto log)
    {
        if (log == null)
            throw new ArgumentNullException(nameof(log), "Log não pode ser nulo");

        // Validar Timestamp
        if (log.Ts == default)
            throw new ArgumentException("Timestamp é obrigatório");

        if (log.Ts > DateTime.UtcNow.AddMinutes(5))
            throw new ArgumentException("Timestamp não pode ser futuro");

        // Validar Level
        if (string.IsNullOrWhiteSpace(log.Level))
            throw new ArgumentException("Level é obrigatório");

        if (!IsValidLogLevel(log.Level))
            throw new ArgumentException($"Level inválido: {log.Level}. Valores válidos: Trace, Debug, Information, Warning, Error, Critical");

        // Validar Message
        if (string.IsNullOrWhiteSpace(log.Message))
            throw new ArgumentException("Message é obrigatória");

        if (log.Message.Length > 5000)
            throw new ArgumentException("Message muito longa (máximo 5000 caracteres)");

        // Validar Tenant
        if (string.IsNullOrWhiteSpace(log.Tenant))
            throw new ArgumentException("Tenant é obrigatório");

        if (log.Tenant.Length > 50)
            throw new ArgumentException("Tenant muito longo (máximo 50 caracteres)");

        // Validar Host (opcional, mas se fornecido deve ser válido)
        if (!string.IsNullOrWhiteSpace(log.Host) && log.Host.Length > 100)
            throw new ArgumentException("Host muito longo (máximo 100 caracteres)");

        // Validar Props (opcional)
        if (log.Props?.Count > 100)
            throw new ArgumentException("Máximo 100 propriedades por log");

        // Validar Exception (se fornecida)
        ValidateException(log.Exception);

        // Validar Trace (se fornecida)
        ValidateTrace(log.Trace);
    }

    private static bool IsValidLogLevel(string level)
    {
        var validLevels = new[] { "Trace", "Debug", "Information", "Warning", "Error", "Critical" };
        return validLevels.Contains(level, StringComparer.OrdinalIgnoreCase);
    }

    private static void ValidateException(ExceptionInfoDto? exception)
    {
        if (exception == null) return;

        if (!string.IsNullOrWhiteSpace(exception.Stack) && exception.Stack.Length > 50000)
            throw new ArgumentException("Stack trace muito longo (máximo 50000 caracteres)");

        if (!string.IsNullOrWhiteSpace(exception.Type) && exception.Type.Length > 500)
            throw new ArgumentException("Exception Type muito longo (máximo 500 caracteres)");

        if (!string.IsNullOrWhiteSpace(exception.Message) && exception.Message.Length > 2000)
            throw new ArgumentException("Exception Message muito longa (máximo 2000 caracteres)");
    }

    private static void ValidateTrace(TraceInfoDto? trace)
    {
        if (trace == null) return;

        // CorrelationId deve ser GUID se fornecido
        if (!string.IsNullOrWhiteSpace(trace.CorrelationId) &&
            !Guid.TryParse(trace.CorrelationId, out _))
            throw new ArgumentException("CorrelationId deve ser um GUID válido");

        // TraceId deve ter formato hexadecimal de 32 caracteres se fornecido
        if (!string.IsNullOrWhiteSpace(trace.TraceId) &&
            !IsValidTraceId(trace.TraceId))
            throw new ArgumentException("TraceId deve ter formato hexadecimal de 32 caracteres");

        // SpanId deve ter formato hexadecimal de 16 caracteres se fornecido
        if (!string.IsNullOrWhiteSpace(trace.SpanId) &&
            !IsValidSpanId(trace.SpanId))
            throw new ArgumentException("SpanId deve ter formato hexadecimal de 16 caracteres");
    }

    private static bool IsValidTraceId(string traceId)
    {
        return traceId.Length == 32 &&
               traceId.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }

    private static bool IsValidSpanId(string spanId)
    {
        return spanId.Length == 16 &&
               spanId.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }
}
