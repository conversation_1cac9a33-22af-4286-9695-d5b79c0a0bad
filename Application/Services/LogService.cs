using System.Security.Cryptography;
using System.Text;
using Zin.Logging.Application.DTOs;
using Zin.Logging.Shared.DTOs;
using Zin.Logging.Domain.Entities;
using Zin.Logging.Domain.Interfaces;
using Zin.Logging.Shared.Utils;

namespace Zin.Logging.Application.Services;

public class LogService
{
    private readonly ILogRepository _logRepository;
    private readonly PiiMasker _piiMasker;

    public LogService(ILogRepository logRepository, PiiMasker piiMasker)
    {
        _logRepository = logRepository;
        _piiMasker = piiMasker;
    }

    public async Task SaveLogs(IEnumerable<LogInputDto> logInputs)
    {
        // Validação do lote
        ValidateBatch(logInputs);

        // Validação individual de cada log
        var logInputsList = logInputs.ToList();
        for (int i = 0; i < logInputsList.Count; i++)
        {
            try
            {
                ValidateLogInput(logInputsList[i]);
            }
            catch (ArgumentException ex)
            {
                throw new ArgumentException($"Erro no log[{i}]: {ex.Message}", ex);
            }
        }

        var logEvents = logInputsList.Select(logInput =>
        {
            _piiMasker.Mask(logInput);
            return new LogEvent
            {
                EventId = Guid.NewGuid(),
                Ts = logInput.Ts,
                Level = logInput.Level,
                Message = logInput.Message,
                Props = logInput.Props?.ToDictionary(p => p.Key, p => BsonConverter.ConvertJsonElement(p.Value)),
                Exception = logInput.Exception != null ? new Domain.Entities.ExceptionInfo
                {
                    Type = logInput.Exception.Type,
                    Message = logInput.Exception.Message,
                    Stack = logInput.Exception.Stack,
                    StackHash = CalculateStackHash(logInput.Exception.Stack)
                } : null,
                Trace = logInput.Trace != null ? new Domain.Entities.TraceInfo
                {
                    CorrelationId = logInput.Trace.CorrelationId,
                    TraceId = logInput.Trace.TraceId,
                    SpanId = logInput.Trace.SpanId
                } : null,
                Tenant = logInput.Tenant,
                Host = logInput.Host,
                SchemaVersion = 1
            };
        });

        await _logRepository.AddLogsAsync(logEvents);
    }

    public async Task<IEnumerable<LogOutputDto>> GetLogsAsync(int page, int pageSize)
    {
        var logEvents = await _logRepository.GetLogsAsync(page, pageSize);
        return logEvents.Select(logEvent => new LogOutputDto
        {
            EventId = logEvent.EventId,
            Ts = logEvent.Ts,
            Level = logEvent.Level,
            Message = logEvent.Message,
            Props = logEvent.Props,
            Tenant = logEvent.Tenant,
            Host = logEvent.Host
        });
    }

    private static string? CalculateStackHash(string? stackTrace)
    {
        if (string.IsNullOrEmpty(stackTrace)) return null;

        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(stackTrace));
        return Convert.ToHexString(hashBytes)[..8];
    }

    private static void ValidateBatch(IEnumerable<LogInputDto> logInputs)
    {
        ArgumentNullException.ThrowIfNull(logInputs, nameof(logInputs));

        var logsList = logInputs.ToList();

        if (!logsList.Any())
            throw new ArgumentException("Nenhum log fornecido");

        if (logsList.Count > 1000)
            throw new ArgumentException("Máximo 1000 logs por lote");
    }

    private static void ValidateLogInput(LogInputDto log)
    {
        ArgumentNullException.ThrowIfNull(log, nameof(log));

        ValidateTimestamp(log.Ts);
        ValidateLevel(log.Level);
        ValidateMessage(log.Message);
        ValidateTenant(log.Tenant);
        ValidateHost(log.Host);
        ValidateProps(log.Props);
        ValidateException(log.Exception);
        ValidateTrace(log.Trace);
    }

    private static void ValidateTimestamp(DateTime timestamp)
    {
        if (timestamp == default)
            throw new ArgumentException("Timestamp é obrigatório");

        if (timestamp > DateTime.UtcNow.AddMinutes(5))
            throw new ArgumentException("Timestamp não pode ser futuro");
    }

    private static void ValidateLevel(string? level)
    {
        if (string.IsNullOrWhiteSpace(level))
            throw new ArgumentException("Level é obrigatório");

        if (!IsValidLogLevel(level))
            throw new ArgumentException($"Level inválido: {level}. Valores válidos: Trace, Debug, Information, Warning, Error, Critical");
    }

    private static void ValidateMessage(string? message)
    {
        if (string.IsNullOrWhiteSpace(message))
            throw new ArgumentException("Message é obrigatória");

        if (message.Length > 5000)
            throw new ArgumentException("Message muito longa (máximo 5000 caracteres)");
    }

    private static void ValidateTenant(string? tenant)
    {
        if (string.IsNullOrWhiteSpace(tenant))
            throw new ArgumentException("Tenant é obrigatório");

        if (tenant.Length > 50)
            throw new ArgumentException("Tenant muito longo (máximo 50 caracteres)");
    }

    private static void ValidateHost(string? host)
    {
        if (!string.IsNullOrWhiteSpace(host) && host.Length > 100)
            throw new ArgumentException("Host muito longo (máximo 100 caracteres)");
    }

    private static void ValidateProps(Dictionary<string, object>? props)
    {
        if (props?.Count > 100)
            throw new ArgumentException("Máximo 100 propriedades por log");
    }

    private static bool IsValidLogLevel(string level)
    {
        var validLevels = new[] { "Trace", "Debug", "Information", "Warning", "Error", "Critical" };
        return validLevels.Contains(level, StringComparer.OrdinalIgnoreCase);
    }

    private static void ValidateException(ExceptionInfoDto? exception)
    {
        if (exception == null) return;

        ValidateStringLength(exception.Stack, 50000, "Stack trace");
        ValidateStringLength(exception.Type, 500, "Exception Type");
        ValidateStringLength(exception.Message, 2000, "Exception Message");
    }

    private static void ValidateTrace(TraceInfoDto? trace)
    {
        if (trace == null) return;

        ValidateCorrelationId(trace.CorrelationId);
        ValidateTraceId(trace.TraceId);
        ValidateSpanId(trace.SpanId);
    }

    private static void ValidateStringLength(string? value, int maxLength, string fieldName)
    {
        if (!string.IsNullOrWhiteSpace(value) && value.Length > maxLength)
            throw new ArgumentException($"{fieldName} muito longo (máximo {maxLength} caracteres)");
    }

    private static void ValidateCorrelationId(string? correlationId)
    {
        if (!string.IsNullOrWhiteSpace(correlationId) && !Guid.TryParse(correlationId, out _))
            throw new ArgumentException("CorrelationId deve ser um GUID válido");
    }

    private static void ValidateTraceId(string? traceId)
    {
        if (!string.IsNullOrWhiteSpace(traceId) && !IsValidTraceId(traceId))
            throw new ArgumentException("TraceId deve ter formato hexadecimal de 32 caracteres");
    }

    private static void ValidateSpanId(string? spanId)
    {
        if (!string.IsNullOrWhiteSpace(spanId) && !IsValidSpanId(spanId))
            throw new ArgumentException("SpanId deve ter formato hexadecimal de 16 caracteres");
    }

    private static bool IsValidTraceId(string traceId)
    {
        return traceId.Length == 32 &&
               traceId.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }

    private static bool IsValidSpanId(string spanId)
    {
        return spanId.Length == 16 &&
               spanId.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }
}
