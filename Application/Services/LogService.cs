using System.Security.Cryptography;
using System.Text;
using Zin.Logging.Application.DTOs;
using Zin.Logging.Shared.DTOs;
using Zin.Logging.Domain.Entities;
using Zin.Logging.Domain.Interfaces;
using Zin.Logging.Shared.Utils;

namespace Zin.Logging.Application.Services;

public class LogService
{
    private readonly ILogRepository _logRepository;
    private readonly PiiMasker _piiMasker;

    public LogService(ILogRepository logRepository, PiiMasker piiMasker)
    {
        _logRepository = logRepository;
        _piiMasker = piiMasker;
    }

    public async Task SaveLogs(IEnumerable<LogInputDto> logInputs)
    {
        var logEvents = logInputs.Select(logInput =>
        {
            _piiMasker.Mask(logInput);
            return new LogEvent
            {
                EventId = Guid.NewGuid(),
                Ts = logInput.Ts,
                Level = logInput.Level,
                Message = logInput.Message,
                Props = logInput.Props?.ToDictionary(p => p.Key, p => BsonConverter.ConvertJsonElement(p.Value)),
                Exception = logInput.Exception != null ? new Domain.Entities.ExceptionInfo
                {
                    Type = logInput.Exception.Type,
                    Message = logInput.Exception.Message,
                    Stack = logInput.Exception.Stack,
                    StackHash = CalculateStackHash(logInput.Exception.Stack)
                } : null,
                Trace = logInput.Trace != null ? new Domain.Entities.TraceInfo
                {
                    CorrelationId = logInput.Trace.CorrelationId,
                    TraceId = logInput.Trace.TraceId,
                    SpanId = logInput.Trace.SpanId
                } : null,
                Tenant = logInput.Tenant,
                Host = logInput.Host,
                SchemaVersion = 1
            };
        });

        await _logRepository.AddLogsAsync(logEvents);
    }

    public async Task<IEnumerable<LogOutputDto>> GetLogsAsync(int page, int pageSize)
    {
        var logEvents = await _logRepository.GetLogsAsync(page, pageSize);
        return logEvents.Select(logEvent => new LogOutputDto
        {
            EventId = logEvent.EventId,
            Ts = logEvent.Ts,
            Level = logEvent.Level,
            Message = logEvent.Message,
            Props = logEvent.Props,
            Tenant = logEvent.Tenant,
            Host = logEvent.Host
        });
    }

    private static string? CalculateStackHash(string? stackTrace)
    {
        if (string.IsNullOrEmpty(stackTrace)) return null;
        var hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(stackTrace));
        return Convert.ToHexString(hashBytes)[..8];
    }
}
