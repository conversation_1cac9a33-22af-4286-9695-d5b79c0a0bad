namespace Zin.Logging.Shared.DTOs;

public class LogInputDto
{
    public DateTime Ts { get; set; }
    public string? Level { get; set; }
    public string? Message { get; set; }
    public Dictionary<string, object>? Props { get; set; }
    public ExceptionInfoDto? Exception { get; set; }
    public TraceInfoDto? Trace { get; set; }
    public string? Tenant { get; set; }
    public string? Host { get; set; }
}

public class ExceptionInfoDto
{
    public string? Type { get; set; }
    public string? Message { get; set; }
    public string? Stack { get; set; }
}

public class TraceInfoDto
{
    public string? CorrelationId { get; set; }
    public string? TraceId { get; set; }
    public string? SpanId { get; set; }
}
