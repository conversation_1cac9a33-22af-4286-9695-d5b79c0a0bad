using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.Shared.Utils;

public class PiiMasker
{
    private static readonly List<string> SensitiveKeys = new()
    {
        "userId", "email", "cpf", "phone", "name"
    };

    public void Mask(LogInputDto log)
    {
        if (log.Props == null) return;

        var keysToMask = log.Props.Keys.Intersect(SensitiveKeys).ToList();

        foreach (var key in keysToMask)
        {
            var value = log.Props[key]?.ToString();
            if (!string.IsNullOrEmpty(value))
            {
                log.Props[key] = MaskValue(value);
            }
        }
    }

    private static string MaskValue(string value)
    {
        if (value.Length <= 4)
            return "***";

        return $"{value.Substring(0, 2)}{new string('*', value.Length - 3)}{value.Substring(value.Length-1, 1)}";
    }
}