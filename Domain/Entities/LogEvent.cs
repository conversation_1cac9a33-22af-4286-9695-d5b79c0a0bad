using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Zin.Logging.Domain.Entities;

public class LogEvent
{
    [BsonId]
    [BsonRepresentation(BsonType.String)]
    public Guid EventId { get; set; }
    public DateTime Ts { get; set; }
    public string? Level { get; set; }
    public string? Message { get; set; }
    public Dictionary<string, object?>? Props { get; set; }
    public ExceptionInfo? Exception { get; set; }
    public TraceInfo? Trace { get; set; }
    public string? Tenant { get; set; }
    public string? Host { get; set; }
    public int SchemaVersion { get; set; }
}

public class ExceptionInfo
{
    public string? Type { get; set; }
    public string? Message { get; set; }
    public string? Stack { get; set; }
    public string? StackHash { get; set; }
}

public class TraceInfo
{
    public string? CorrelationId { get; set; }
    public string? TraceId { get; set; }
    public string? SpanId { get; set; }
}