using FluentValidation;
using FluentValidation.AspNetCore;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Zin.Logging.Application.Services;
using Zin.Logging.Application.Validators;
using Zin.Logging.Domain.Interfaces;
using Zin.Logging.Infrastructure.Config;
using Zin.Logging.Infrastructure.Mongo;
using Zin.Logging.Shared.DTOs;
using Zin.Logging.Shared.Utils;


BsonSerializer.RegisterSerializer(new GuidSerializer(BsonType.String));

var builder = WebApplication.CreateBuilder(args);
var cfg = builder.Configuration;

// Flags
bool tracingEnabled = cfg.GetValue<bool>("Observability:TracingEnabled", false);
bool useConsoleExporter = cfg.GetValue<bool>("Observability:UseConsoleExporter", false);

string serviceName = cfg.GetValue<string>("Service:Name", builder.Environment.ApplicationName);
string environmentName = builder.Environment.EnvironmentName;

// OpenTelemetry (condicional)
if (tracingEnabled)
{
    builder.Services.AddOpenTelemetry()
        .ConfigureResource(r => r
            .AddService(serviceName, serviceVersion: typeof(Program).Assembly.GetName().Version?.ToString())
            .AddAttributes(new[] {
                new KeyValuePair<string, object>("deployment.environment", environmentName)
            }))
        .WithTracing(t =>
        {
            t.AddAspNetCoreInstrumentation();
            t.AddHttpClientInstrumentation();
            t.AddOtlpExporter();

            if (useConsoleExporter) // Console exporter para testar
            {
                t.AddConsoleExporter();
            }
        });
}


builder.Services.Configure<MongoDbSettings>(builder.Configuration.GetSection("MongoDbSettings"));

builder.Services.AddSingleton<IMongoClient>(sp => 
    new MongoClient(builder.Configuration.GetValue<string>("MongoDbSettings:ConnectionString")));

builder.Services.AddScoped<LogService>();
builder.Services.AddScoped<ILogRepository, LogRepository>();
builder.Services.AddScoped<PiiMasker>();

builder.Services.AddValidatorsFromAssemblyContaining<LogInputDtoValidator>();

builder.Services.AddControllers();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseCors();

app.UseAuthorization();

app.MapControllers();

app.Run();
