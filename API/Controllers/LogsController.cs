using Microsoft.AspNetCore.Mvc;
using Zin.Logging.Application.DTOs;
using Zin.Logging.Shared.DTOs;
using Zin.Logging.Application.Services;

namespace Zin.Logging.API.Controllers;

[ApiController]
[Route("[controller]")]
public class LogsController : ControllerBase
{
    private readonly LogService _logService;

    public LogsController(LogService logService)
    {
        _logService = logService;
    }

    [HttpPost]
    public async Task<IActionResult> Post(IEnumerable<LogInputDto> logInputs)
    {
        // Validação básica do lote (FluentValidation cuida da validação individual)
        if (logInputs == null || !logInputs.Any())
            return BadRequest("Nenhum log fornecido");

        var logsList = logInputs.ToList();
        if (logsList.Count > 1000)
            return BadRequest("Máximo 1000 logs por lote");

        // Se chegou até aqui, FluentValidation já validou cada item individualmente
        await _logService.SaveLogs(logsList);
        return Ok(new { processed = logsList.Count });
    }

    [HttpGet]
    public async Task<IActionResult> Get([FromQuery] int page = 1, [FromQuery] int pageSize = 100)
    {
        var logs = await _logService.GetLogsAsync(page, pageSize);
        return Ok(logs);
    }
}
