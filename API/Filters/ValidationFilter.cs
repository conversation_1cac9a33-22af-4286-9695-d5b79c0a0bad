using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Collections;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.API.Filters;

public class ValidationFilter : IAsyncActionFilter
{
    private readonly IServiceProvider _serviceProvider;

    public ValidationFilter(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        foreach (var parameter in context.ActionDescriptor.Parameters)
        {
            if (context.ActionArguments.TryGetValue(parameter.Name, out var value))
            {
                // Validar IEnumerable<LogInputDto>
                if (value is IEnumerable<LogInputDto> logInputs)
                {
                    var validator = _serviceProvider.GetService<IValidator<LogInputDto>>();
                    if (validator != null)
                    {
                        var validationErrors = new List<string>();
                        var logsList = logInputs.ToList();

                        // Validação básica do lote
                        if (!logsList.Any())
                        {
                            context.Result = new BadRequestObjectResult("Nenhum log fornecido");
                            return;
                        }

                        if (logsList.Count > 1000)
                        {
                            context.Result = new BadRequestObjectResult("Máximo 1000 logs por lote");
                            return;
                        }

                        // Validar cada item
                        for (int i = 0; i < logsList.Count; i++)
                        {
                            var validationResult = await validator.ValidateAsync(logsList[i]);
                            if (!validationResult.IsValid)
                            {
                                var errors = validationResult.Errors.Select(e => $"Log[{i}]: {e.ErrorMessage}");
                                validationErrors.AddRange(errors);
                            }
                        }

                        if (validationErrors.Any())
                        {
                            context.Result = new BadRequestObjectResult(new { errors = validationErrors });
                            return;
                        }
                    }
                }
                // Validar LogInputDto individual
                else if (value is LogInputDto logInput)
                {
                    var validator = _serviceProvider.GetService<IValidator<LogInputDto>>();
                    if (validator != null)
                    {
                        var validationResult = await validator.ValidateAsync(logInput);
                        if (!validationResult.IsValid)
                        {
                            var errors = validationResult.Errors.Select(e => e.ErrorMessage);
                            context.Result = new BadRequestObjectResult(new { errors });
                            return;
                        }
                    }
                }
            }
        }

        await next();
    }
}
